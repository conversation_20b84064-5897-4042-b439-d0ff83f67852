# Ptrade版本 - 集合竞价三合一策略
# 原策略来源：
# 克隆自聚宽文章：https://www.joinquant.com/post/49474
# 标题：集合竞价三合一，今年收益1067%，年化198%
# 作者：jevon y

# 克隆自聚宽文章：https://www.joinquant.com/post/44901
# 标题：首板低开策略
# 作者：wywy1995

# 克隆自聚宽文章：https://www.joinquant.com/post/48523
# 标题：一进二集合竞价策略
# 作者：十足的小市值迷

# 克隆自聚宽文章：https://www.joinquant.com/post/49364
# 标题：一种弱转强的选股策略，年化100%以上
# 作者：紫露薇霜

# 2024/08/01  止损卖出修改为跌破5日均线
# 2024/12/XX  改造为Ptrade平台版本

#coding:utf-8

import pandas as pd
import numpy as np
import datetime as dt
from datetime import datetime
from datetime import timedelta
import math


def initialize(context):
    # Ptrade平台初始化设置
    # 设置股票池为全A股
    g.stock_pool = []
    
    # 一进二
    run_daily(get_stock_list, time='9:01')
    run_daily(buy, time='09:31')
    run_daily(sell, time='11:25')
    run_daily(sell, time='14:50')


# 选股
def get_stock_list(context):
    # 获取当前交易日和前几个交易日
    current_date = get_trading_day()
    all_trade_days = get_all_trades_days()
    
    # 找到当前日期在交易日列表中的位置
    current_index = None
    for i, trade_day in enumerate(all_trade_days):
        if trade_day.strftime('%Y-%m-%d') == current_date.strftime('%Y-%m-%d'):
            current_index = i
            break
    
    if current_index is None or current_index < 2:
        g.target_list = []
        g.target_list2 = []
        return
    
    # 获取前几个交易日
    date = all_trade_days[current_index - 1].strftime('%Y-%m-%d')
    date_1 = all_trade_days[current_index - 2].strftime('%Y-%m-%d') if current_index >= 2 else None
    date_2 = all_trade_days[current_index - 3].strftime('%Y-%m-%d') if current_index >= 3 else None

    # 初始列表
    initial_list = prepare_stock_list(date)
    # 昨日涨停
    hl_list = get_hl_stock(initial_list, date)
    # 前日曾涨停
    hl1_list = get_ever_hl_stock(initial_list, date_1) if date_1 else []
    # 前前日曾涨停
    hl2_list = get_ever_hl_stock(initial_list, date_2) if date_2 else []
    # 合并 hl1_list 和 hl2_list 为一个集合，用于快速查找需要剔除的元素
    elements_to_remove = set(hl1_list + hl2_list)
    # 使用列表推导式来剔除 hl_list 中存在于 elements_to_remove 集合中的元素
    hl_list = [stock for stock in hl_list if stock not in elements_to_remove]

    g.target_list = hl_list

    # 昨日曾涨停
    h1_list = get_ever_hl_stock2(initial_list, date)
    # 上上个交易日涨停过滤
    elements_to_remove = get_hl_stock(initial_list, date_1) if date_1 else []

    # 过滤上上个交易日涨停、曾涨停
    all_list = [stock for stock in h1_list if stock not in elements_to_remove]

    g.target_list2 = all_list


# 交易
def buy(context):
    # 先进行择时判断
    log.info('开始进行择时判断...')
    timing_result = select_timing(context)
    log.info('择时判断结果: {}'.format(timing_result))

    if not timing_result:
        log.info('今日择时信号不满足，不进行交易')
        return

    log.info('择时信号满足，开始选股...')
    qualified_stocks = []
    gk_stocks = []
    dk_stocks = []
    rzq_stocks = []
    
    current_date = get_trading_day()
    date_now = current_date.strftime("%Y-%m-%d")
    mid_time1 = ' 09:15:00'
    end_times1 = ' 09:26:00'
    start = date_now + mid_time1
    end = date_now + end_times1
    
    # 高开
    for s in g.target_list:
        try:
            # 条件一：均价，金额，市值，换手率
            prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s)
            if prev_day_data.empty:
                continue
            
            avg_price_increase_value = prev_day_data['money'].iloc[-1] / prev_day_data['volume'].iloc[-1] / prev_day_data['close'].iloc[-1] * 1.1 - 1
            if avg_price_increase_value < 0.07 or prev_day_data['money'].iloc[-1] < 5.5e8 or prev_day_data['money'].iloc[-1] > 20e8:
                continue
            
            # 获取基本面数据（市值、换手率）
            try:
                fundamentals_data = get_fundamentals(s, 'valuation', 'market_cap,circulating_market_cap,turnover_ratio')
                if not fundamentals_data:
                    continue

                # 检查市值条件
                market_cap = fundamentals_data.get('market_cap', 0) if isinstance(fundamentals_data, dict) else 0
                circulating_market_cap = fundamentals_data.get('circulating_market_cap', 0) if isinstance(fundamentals_data, dict) else 0
            except Exception as e:
                continue
            
            if market_cap < 70 or circulating_market_cap > 520:
                continue

            # 条件二：左压
            zyts = calculate_zyts(s, context)
            volume_data = get_history(zyts, '1d', ['volume'], s)
            if volume_data.empty or len(volume_data) < 2:
                continue
            if volume_data['volume'].iloc[-1] <= volume_data['volume'].iloc[:-1].max() * 0.9:
                continue

            # 条件三：高开,开比
            auction_data = get_trend_data(s, start_date=start, end_date=end)
            if auction_data.empty:
                continue
            
            # 获取当前快照数据
            snapshot = get_snapshot(s)
            if s not in snapshot:
                continue
                
            current_price = snapshot[s]['last_px']
            high_limit = snapshot[s]['up_px']
            
            # 检查集合竞价条件
            auction_volume = auction_data['volume'].iloc[0] if not auction_data.empty else 0
            if auction_volume / volume_data['volume'].iloc[-1] < 0.03:
                continue
                
            current_ratio = current_price / (high_limit / 1.1)
            if current_ratio <= 1 or current_ratio >= 1.06:
                continue

            # 如果股票满足所有条件，则添加到列表中
            gk_stocks.append(s)
            qualified_stocks.append(s)
            
        except Exception as e:
            log.info('处理股票 {} 时出错: {}'.format(s, str(e)))
            continue

    # 低开策略处理
    process_low_open_strategy(context, qualified_stocks, dk_stocks, date_now)
    
    # 弱转强策略处理  
    process_weak_to_strong_strategy(context, qualified_stocks, rzq_stocks, start, end)

    if len(qualified_stocks) > 0:
        log.info('———————————————————————————————————')
        log.info('今日选股：{}'.format(','.join(qualified_stocks)))
        log.info('一进二：{}'.format(','.join(gk_stocks)))
        log.info('首板低开：{}'.format(','.join(dk_stocks)))
        log.info('弱转强：{}'.format(','.join(rzq_stocks)))
        log.info('———————————————————————————————————')
    else:
        log.info('今日无目标个股')

    # 执行买入操作
    if len(qualified_stocks) != 0 and context.portfolio.cash / context.portfolio.total_value > 0.3:
        value = context.portfolio.cash / len(qualified_stocks)
        for s in qualified_stocks:
            try:
                # 获取当前价格
                snapshot = get_snapshot(s)
                if s not in snapshot:
                    continue
                    
                current_price = snapshot[s]['last_px']
                day_open = snapshot[s]['open_px']
                
                # 下单
                if context.portfolio.cash / current_price > 100:
                    order_value(s, value)
                    log.info('买入 {}'.format(s))
                    log.info('———————————————————————————————————')
            except Exception as e:
                log.info('买入股票 {} 时出错: {}'.format(s, str(e)))
                continue


def process_low_open_strategy(context, qualified_stocks, dk_stocks, date_now):
    """处理低开策略"""
    try:
        # 基础信息
        date = get_shifted_date(date_now, -1, 'T')
        
        # 昨日涨停列表
        initial_list = prepare_stock_list2(date)
        hl_list = get_hl_stock(initial_list, date)

        if len(hl_list) != 0:
            # 获取非连板涨停的股票
            ccd = get_continue_count_df(hl_list, date, 10)
            lb_list = list(ccd.index) if not ccd.empty else []
            stock_list = [s for s in hl_list if s not in lb_list]

            # 计算相对位置
            rpd = get_relative_position_df(stock_list, date, 60)
            if not rpd.empty:
                rpd = rpd[rpd['rp'] <= 0.5]
                stock_list = list(rpd.index)

            # 低开检查
            if len(stock_list) > 0:
                for s in stock_list:
                    try:
                        # 获取昨日收盘价
                        prev_close_data = get_history(1, '1d', ['close'], s)
                        if prev_close_data.empty:
                            continue
                            
                        prev_close = prev_close_data['close'].iloc[-1]
                        
                        # 获取今日开盘价
                        snapshot = get_snapshot(s)
                        if s not in snapshot:
                            continue
                            
                        day_open = snapshot[s]['open_px']
                        open_pct = day_open / prev_close
                        
                        if 0.955 <= open_pct <= 0.97:  # 低开越多风险越大，选择3个多点即可
                            prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s)
                            if not prev_day_data.empty and prev_day_data['money'].iloc[-1] >= 1e8:
                                dk_stocks.append(s)
                                qualified_stocks.append(s)
                    except Exception as e:
                        log.info('处理低开股票 {} 时出错: {}'.format(s, str(e)))
                        continue
    except Exception as e:
        log.info('处理低开策略时出错: {}'.format(str(e)))


def process_weak_to_strong_strategy(context, qualified_stocks, rzq_stocks, start, end):
    """处理弱转强策略"""
    try:
        for s in g.target_list2:
            try:
                # 过滤前面三天涨幅超过28%的票
                price_data = get_history(4, '1d', ['close'], s)
                if price_data.empty or len(price_data) < 4:
                    continue
                    
                increase_ratio = (price_data['close'].iloc[-1] - price_data['close'].iloc[0]) / price_data['close'].iloc[0]
                if increase_ratio > 0.28:
                    continue

                # 过滤前一日收盘价小于开盘价5%以上的票
                prev_day_data = get_history(1, '1d', ['open', 'close'], s)
                if prev_day_data.empty:
                    continue
                    
                open_close_ratio = (prev_day_data['close'].iloc[-1] - prev_day_data['open'].iloc[-1]) / prev_day_data['open'].iloc[-1]
                if open_close_ratio < -0.05:
                    continue

                prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s)
                if prev_day_data.empty:
                    continue
                    
                avg_price_increase_value = prev_day_data['money'].iloc[-1] / prev_day_data['volume'].iloc[-1] / prev_day_data['close'].iloc[-1] - 1
                if avg_price_increase_value < -0.04 or prev_day_data['money'].iloc[-1] < 3e8 or prev_day_data['money'].iloc[-1] > 19e8:
                    continue
                    
                # 获取基本面数据
                try:
                    fundamentals_data = get_fundamentals(s, 'valuation', 'market_cap,circulating_market_cap,turnover_ratio')
                    if not fundamentals_data:
                        continue

                    market_cap = fundamentals_data.get('market_cap', 0) if isinstance(fundamentals_data, dict) else 0
                    circulating_market_cap = fundamentals_data.get('circulating_market_cap', 0) if isinstance(fundamentals_data, dict) else 0
                except Exception as e:
                    continue
                
                if market_cap < 70 or circulating_market_cap > 520:
                    continue

                zyts = calculate_zyts(s, context)
                volume_data = get_history(zyts, '1d', ['volume'], s)
                if volume_data.empty or len(volume_data) < 2:
                    continue
                if volume_data['volume'].iloc[-1] <= volume_data['volume'].iloc[:-1].max() * 0.9:
                    continue

                auction_data = get_trend_data(s, start_date=start, end_date=end)
                if auction_data.empty:
                    continue

                auction_volume = auction_data['volume'].iloc[0] if not auction_data.empty else 0
                if auction_volume / volume_data['volume'].iloc[-1] < 0.03:
                    continue
                    
                # 获取当前快照数据
                snapshot = get_snapshot(s)
                if s not in snapshot:
                    continue
                    
                current_price = snapshot[s]['last_px']
                high_limit = snapshot[s]['up_px']
                
                current_ratio = current_price / (high_limit / 1.1)
                if current_ratio <= 0.98 or current_ratio >= 1.09:
                    continue
                    
                rzq_stocks.append(s)
                qualified_stocks.append(s)
                
            except Exception as e:
                log.info('处理弱转强股票 {} 时出错: {}'.format(s, str(e)))
                continue
    except Exception as e:
        log.info('处理弱转强策略时出错: {}'.format(str(e)))


# 处理日期相关函数
def transform_date(date, date_type):
    if type(date) == str:
        str_date = date
        dt_date = dt.datetime.strptime(date, '%Y-%m-%d')
        d_date = dt_date.date()
    elif type(date) == dt.datetime:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = date
        d_date = dt_date.date()
    elif type(date) == dt.date:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = dt.datetime.strptime(str_date, '%Y-%m-%d')
        d_date = date
    dct = {'str': str_date, 'dt': dt_date, 'd': d_date}
    return dct[date_type]


def get_shifted_date(date, days, days_type='T'):
    """获取偏移日期"""
    try:
        # 获取上一个自然日
        d_date = transform_date(date, 'd')
        yesterday = d_date + dt.timedelta(-1)

        # 移动days个自然日
        if days_type == 'N':
            shifted_date = yesterday + dt.timedelta(days + 1)
            return str(shifted_date)

        # 移动days个交易日
        if days_type == 'T':
            all_trade_days = get_all_trades_days()
            all_trade_days_str = [i.strftime('%Y-%m-%d') for i in all_trade_days]

            # 如果上一个自然日是交易日，根据其在交易日列表中的index计算平移后的交易日
            if str(yesterday) in all_trade_days_str:
                index = all_trade_days_str.index(str(yesterday))
                target_index = index + days + 1
                if 0 <= target_index < len(all_trade_days_str):
                    return all_trade_days_str[target_index]
            else:
                # 否则，从上一个自然日向前数，先找到最近一个交易日，再开始平移
                for i in range(100):
                    last_trade_date = yesterday - dt.timedelta(i)
                    if str(last_trade_date) in all_trade_days_str:
                        index = all_trade_days_str.index(str(last_trade_date))
                        target_index = index + days + 1
                        if 0 <= target_index < len(all_trade_days_str):
                            return all_trade_days_str[target_index]
                        break
        return date  # 如果无法计算，返回原日期
    except Exception as e:
        log.info('计算偏移日期时出错: {}'.format(str(e)))
        return date


# 过滤函数
def filter_new_stock(initial_list, date, days=50):
    """过滤新股"""
    try:
        d_date = transform_date(date, 'd')
        filtered_list = []
        for stock in initial_list:
            try:
                stock_info = get_stock_info(stock)
                if stock_info and 'start_date' in stock_info:
                    start_date = stock_info['start_date']
                    if isinstance(start_date, str):
                        start_date = dt.datetime.strptime(start_date, '%Y-%m-%d').date()
                    elif isinstance(start_date, dt.datetime):
                        start_date = start_date.date()

                    if d_date - start_date > dt.timedelta(days=days):
                        filtered_list.append(stock)
            except Exception as e:
                continue
        return filtered_list
    except Exception as e:
        log.info('过滤新股时出错: {}'.format(str(e)))
        return initial_list


def filter_st_stock(initial_list, date):
    """过滤ST股票"""
    try:
        filtered_list = []
        for stock in initial_list:
            try:
                # 使用get_stock_status检查ST状态
                status_info = get_stock_status(stock, query_type='ST')
                # 如果没有ST状态信息，说明不是ST股票
                if not status_info or len(status_info) == 0:
                    filtered_list.append(stock)
            except Exception as e:
                # 如果获取状态失败，保守起见不过滤
                filtered_list.append(stock)
        return filtered_list
    except Exception as e:
        log.info('过滤ST股票时出错: {}'.format(str(e)))
        return initial_list


def filter_kcbj_stock(initial_list):
    """过滤科创板、北交所股票"""
    return [stock for stock in initial_list
            if not stock.startswith('4')
            and not stock.startswith('8')
            # and not stock.startswith('3')  # 保留创业板
            and not stock.startswith('68')]


def filter_paused_stock(initial_list, date):
    """过滤停牌股票"""
    try:
        filtered_list = []
        for stock in initial_list:
            try:
                # 使用get_stock_status检查停牌状态
                status_info = get_stock_status(stock, query_type='HALT')
                # 如果没有停牌状态信息，说明没有停牌
                if not status_info or len(status_info) == 0:
                    filtered_list.append(stock)
            except Exception as e:
                # 如果获取状态失败，保守起见不过滤
                filtered_list.append(stock)
        return filtered_list
    except Exception as e:
        log.info('过滤停牌股票时出错: {}'.format(str(e)))
        return initial_list


# 一字涨停过滤
def filter_extreme_limit_stock(context, stock_list, date):
    """过滤一字涨停股票"""
    try:
        tmp = []
        for stock in stock_list:
            try:
                price_data = get_history(1, '1d', ['low', 'high_limit'], stock)
                if not price_data.empty:
                    if price_data['low'].iloc[0] < price_data['high_limit'].iloc[0]:
                        tmp.append(stock)
            except Exception as e:
                continue
        return tmp
    except Exception as e:
        log.info('过滤一字涨停股票时出错: {}'.format(str(e)))
        return stock_list


# 每日初始股票池
def prepare_stock_list(date):
    """准备股票池"""
    try:
        # 获取A股列表
        initial_list = get_Ashares(date)
        if not initial_list:
            return []

        initial_list = filter_kcbj_stock(initial_list)
        initial_list = filter_new_stock(initial_list, date)
        initial_list = filter_st_stock(initial_list, date)
        initial_list = filter_paused_stock(initial_list, date)
        return initial_list
    except Exception as e:
        log.info('准备股票池时出错: {}'.format(str(e)))
        return []


# 计算左压天数
def calculate_zyts(s, context):
    """计算左压天数"""
    try:
        high_prices = get_history(101, '1d', ['high'], s)
        if high_prices.empty or len(high_prices) < 3:
            return 100

        high_values = high_prices['high'].values
        prev_high = high_values[-1]

        zyts_0 = 100
        for i in range(2, min(len(high_values), 100)):
            if high_values[-(i+1)] >= prev_high:
                zyts_0 = i - 1
                break

        zyts = zyts_0 + 5
        return zyts
    except Exception as e:
        log.info('计算左压天数时出错: {}'.format(str(e)))
        return 100


# 筛选出某一日涨停的股票
def get_hl_stock(initial_list, date):
    """获取涨停股票"""
    try:
        hl_list = []
        for stock in initial_list:
            try:
                price_data = get_history(1, '1d', ['close', 'high_limit'], stock)
                if not price_data.empty:
                    close_price = price_data['close'].iloc[-1]
                    high_limit = price_data['high_limit'].iloc[-1]
                    if abs(close_price - high_limit) < 0.01:  # 考虑浮点数精度
                        hl_list.append(stock)
            except Exception as e:
                continue
        return hl_list
    except Exception as e:
        log.info('获取涨停股票时出错: {}'.format(str(e)))
        return []


# 筛选曾涨停
def get_ever_hl_stock(initial_list, date):
    """获取曾涨停股票"""
    try:
        if not date:
            return []

        hl_list = []
        for stock in initial_list:
            try:
                price_data = get_history(1, '1d', ['high', 'high_limit'], stock)
                if not price_data.empty:
                    high_price = price_data['high'].iloc[-1]
                    high_limit = price_data['high_limit'].iloc[-1]
                    if abs(high_price - high_limit) < 0.01:  # 考虑浮点数精度
                        hl_list.append(stock)
            except Exception as e:
                continue
        return hl_list
    except Exception as e:
        log.info('获取曾涨停股票时出错: {}'.format(str(e)))
        return []


# 筛选曾涨停（收盘价不等于涨停价）
def get_ever_hl_stock2(initial_list, date):
    """获取曾涨停但收盘未涨停的股票"""
    try:
        hl_list = []
        for stock in initial_list:
            try:
                price_data = get_history(1, '1d', ['close', 'high', 'high_limit'], stock)
                if not price_data.empty:
                    close_price = price_data['close'].iloc[-1]
                    high_price = price_data['high'].iloc[-1]
                    high_limit = price_data['high_limit'].iloc[-1]

                    # 曾涨停但收盘未涨停
                    if (abs(high_price - high_limit) < 0.01 and
                        abs(close_price - high_limit) >= 0.01):
                        hl_list.append(stock)
            except Exception as e:
                continue
        return hl_list
    except Exception as e:
        log.info('获取曾涨停但收盘未涨停股票时出错: {}'.format(str(e)))
        return []


# 计算涨停数
def get_hl_count_df(hl_list, date, watch_days):
    """计算涨停数"""
    try:
        if not hl_list:
            return pd.DataFrame()

        hl_count_list = []
        extreme_hl_count_list = []

        for stock in hl_list:
            try:
                price_data = get_history(watch_days, '1d', ['close', 'high_limit', 'low'], stock)
                if price_data.empty:
                    hl_count_list.append(0)
                    extreme_hl_count_list.append(0)
                    continue

                # 计算涨停天数
                hl_days = sum(abs(price_data['close'] - price_data['high_limit']) < 0.01)
                # 计算一字涨停天数
                extreme_hl_days = sum(abs(price_data['low'] - price_data['high_limit']) < 0.01)

                hl_count_list.append(hl_days)
                extreme_hl_count_list.append(extreme_hl_days)
            except Exception as e:
                hl_count_list.append(0)
                extreme_hl_count_list.append(0)

        # 创建DataFrame记录
        df = pd.DataFrame(index=hl_list, data={'count': hl_count_list, 'extreme_count': extreme_hl_count_list})
        return df
    except Exception as e:
        log.info('计算涨停数时出错: {}'.format(str(e)))
        return pd.DataFrame()


# 计算连板数
def get_continue_count_df(hl_list, date, watch_days):
    """计算连板数"""
    try:
        if not hl_list:
            return pd.DataFrame()

        df = pd.DataFrame()
        for d in range(2, watch_days + 1):
            HLC = get_hl_count_df(hl_list, date, d)
            if not HLC.empty:
                CHLC = HLC[HLC['count'] == d]
                df = pd.concat([df, CHLC])

        if df.empty:
            return pd.DataFrame()

        stock_list = list(set(df.index))
        ccd = pd.DataFrame()

        for s in stock_list:
            try:
                tmp = df.loc[[s]]
                if len(tmp) > 1:
                    M = tmp['count'].max()
                    tmp = tmp[tmp['count'] == M]
                ccd = pd.concat([ccd, tmp])
            except Exception as e:
                continue

        if not ccd.empty:
            ccd = ccd.sort_values(by='count', ascending=False)
        return ccd
    except Exception as e:
        log.info('计算连板数时出错: {}'.format(str(e)))
        return pd.DataFrame()


# 计算昨涨幅
def get_index_increase_ratio(index_code, context):
    """计算指数涨幅"""
    try:
        # 获取指数昨天和前天的收盘价
        close_prices = get_history(2, '1d', ['close'], index_code)
        if close_prices.empty or len(close_prices) < 2:
            return 0  # 如果数据不足，返回0

        day_before_yesterday_close = close_prices['close'].iloc[0]
        yesterday_close = close_prices['close'].iloc[1]

        # 计算涨幅
        increase_ratio = (yesterday_close - day_before_yesterday_close) / day_before_yesterday_close
        return increase_ratio
    except Exception as e:
        log.info('计算指数涨幅时出错: {}'.format(str(e)))
        return 0


# 获取股票所属行业
def getStockIndustry(stocks):
    """获取股票行业信息"""
    try:
        industry_dict = {}
        for stock in stocks:
            try:
                # 使用get_stock_blocks获取股票所属板块信息
                blocks_info = get_stock_blocks(stock)
                if blocks_info and len(blocks_info) > 0:
                    # 寻找申万一级行业
                    for block in blocks_info:
                        if 'sw_l1' in block or '申万' in str(block):
                            industry_dict[stock] = block.get('industry_name', '未知行业')
                            break
                    else:
                        # 如果没有找到申万一级，使用第一个行业
                        industry_dict[stock] = blocks_info[0].get('industry_name', '未知行业')
            except Exception as e:
                industry_dict[stock] = '未知行业'
        return pd.Series(industry_dict)
    except Exception as e:
        log.info('获取行业信息时出错: {}'.format(str(e)))
        return pd.Series()


# 获取市场宽度
def get_market_breadth(context):
    """获取市场宽度"""
    try:
        log.info("开始计算市场宽度...")
        # 指定日期防止未来数据
        current_date = get_trading_day()
        all_trade_days = get_all_trades_days()

        # 找到昨天的交易日
        yesterday = None
        for i, trade_day in enumerate(all_trade_days):
            if trade_day.strftime('%Y-%m-%d') == current_date.strftime('%Y-%m-%d') and i > 0:
                yesterday = all_trade_days[i - 1]
                break

        if not yesterday:
            return ""

        log.info('使用日期: {}'.format(yesterday.strftime('%Y-%m-%d')))

        # 获取初始列表 - 使用中证全指
        stocks = get_index_stocks("000985.XSHG")
        if not stocks:
            return ""

        log.info('获取到 {} 只股票'.format(len(stocks)))

        # 获取股票价格数据
        count = 1
        valid_stocks = []
        close_data = {}

        for stock in stocks[:500]:  # 限制数量避免超时
            try:
                h = get_history(count + 20, '1d', ['close'], stock)
                if not h.empty and len(h) >= 20:
                    valid_stocks.append(stock)
                    close_data[stock] = h['close'].values
            except Exception as e:
                continue

        if len(valid_stocks) == 0:
            return ""

        log.info('有效数据股票数量: {}'.format(len(valid_stocks)))

        # 计算20日均线和偏离程度
        bias_data = {}
        for stock in valid_stocks:
            try:
                closes = close_data[stock]
                if len(closes) >= 20:
                    ma20 = np.mean(closes[-20:])
                    current_price = closes[-1]
                    bias_data[stock] = current_price > ma20
            except Exception as e:
                continue

        # 获取股票所属行业
        industry_series = getStockIndustry(valid_stocks)
        log.info('获取到 {} 只股票的行业信息'.format(len(industry_series)))

        # 计算行业偏离比例
        industry_bias = {}
        industry_count = {}

        for stock in valid_stocks:
            if stock in bias_data and stock in industry_series:
                industry = industry_series[stock]
                if industry not in industry_bias:
                    industry_bias[industry] = 0
                    industry_count[industry] = 0

                if bias_data[stock]:
                    industry_bias[industry] += 1
                industry_count[industry] += 1

        # 计算比例并找到最高的行业
        industry_ratios = {}
        for industry in industry_bias:
            if industry_count[industry] > 0:
                industry_ratios[industry] = (industry_bias[industry] / industry_count[industry]) * 100

        if not industry_ratios:
            return ""

        # 获取偏离程度最高的行业（仅获取Top1）
        top_industry = max(industry_ratios, key=industry_ratios.get)
        log.info("市场宽度计算结果 - 领先行业Top1: {}".format(top_industry))
        return top_industry
    except Exception as e:
        log.info('市场宽度计算失败: {}'.format(str(e)))
        # 出错时返回空字符串
        return ""


# 择时判断
def select_timing(context):
    """择时判断"""
    log.info("开始执行择时判断函数...")
    try:
        # 获取市场宽度Top1行业
        top_industry = get_market_breadth(context)
        log.info("获取到的市场宽度领先行业Top1: {}".format(top_industry))

        # 需要监控的行业
        restricted_industries = ["银行", "有色金属", "钢铁", "煤炭"]
        log.info("需要监控的行业: {}".format(restricted_industries))

        # 检查Top1行业是否在需要监控的行业中
        is_restricted = False
        for industry in restricted_industries:
            if industry in top_industry:
                is_restricted = True
                log.info("Top1行业 '{}' 包含监控行业 '{}'".format(top_industry, industry))
                break

        if not is_restricted:
            log.info("Top1行业不在监控行业中，择时条件满足，可以交易")
            return True
        else:
            log.info("Top1行业在监控行业中，择时条件不满足，不进行交易")
            return False
    except Exception as e:
        log.info("择时判断出错: {}".format(str(e)))
        # 出错时默认允许交易
        return True


# 上午有利润就跑
def sell(context):
    """卖出函数"""
    try:
        # 获取当前时间
        current_time = get_trading_day()
        current_time_str = current_time.strftime('%H:%M:%S')

        # 根据时间执行不同的卖出策略
        # 注意：这里需要根据实际调用时间来判断，而不是当前系统时间
        # 在Ptrade中，可以通过run_daily的时间参数来控制执行时间
        # 这里简化处理，通过context或其他方式来区分调用时机

        # 获取当前小时和分钟
        current_hour = current_time.hour
        current_minute = current_time.minute

        # 11:25 止盈卖出
        if current_hour == 11 and current_minute == 25:
            for s in list(context.portfolio.positions.keys()):
                try:
                    position = get_position(s)
                    if position.amount == 0:
                        continue

                    # 获取当前价格信息
                    snapshot = get_snapshot(s)
                    if s not in snapshot:
                        continue

                    last_price = snapshot[s]['last_px']
                    high_limit = snapshot[s]['up_px']

                    # 检查是否有利润且未涨停
                    if (position.amount > 0 and
                        last_price < high_limit and
                        last_price > 1 * position.avg_cost):
                        order_target_value(s, 0)

                        # 获取股票名称
                        stock_info = get_stock_info(s)
                        stock_name = stock_info.get('display_name', s) if stock_info else s

                        log.info('止盈卖出 {} {}'.format(s, stock_name))
                        log.info('———————————————————————————————————')
                except Exception as e:
                    log.info('处理止盈卖出股票 {} 时出错: {}'.format(s, str(e)))
                    continue

        # 14:50 止损卖出
        elif current_hour == 14 and current_minute == 50:
            for s in list(context.portfolio.positions.keys()):
                try:
                    position = get_position(s)
                    if position.amount == 0:
                        continue

                    # 获取当前价格信息
                    snapshot = get_snapshot(s)
                    if s not in snapshot:
                        continue

                    last_price = snapshot[s]['last_px']
                    high_limit = snapshot[s]['up_px']

                    # 计算5日均线
                    close_data2 = get_history(4, '1d', ['close'], s)
                    if close_data2.empty or len(close_data2) < 4:
                        continue

                    M4 = close_data2['close'].mean()
                    MA5 = (M4 * 4 + last_price) / 5

                    # 获取股票名称
                    stock_info = get_stock_info(s)
                    stock_name = stock_info.get('display_name', s) if stock_info else s

                    # 止盈条件
                    if (position.amount > 0 and
                        last_price < high_limit and
                        last_price > 1 * position.avg_cost):
                        order_target_value(s, 0)
                        log.info('止盈卖出 {} {}'.format(s, stock_name))
                        log.info('———————————————————————————————————')
                    # 止损条件：跌破5日均线
                    elif position.amount > 0 and last_price < MA5:
                        order_target_value(s, 0)
                        log.info('止损卖出 {} {}'.format(s, stock_name))
                        log.info('———————————————————————————————————')
                except Exception as e:
                    log.info('处理止损卖出股票 {} 时出错: {}'.format(s, str(e)))
                    continue
    except Exception as e:
        log.info('卖出函数执行时出错: {}'.format(str(e)))


# 首版低开策略代码
def filter_new_stock2(initial_list, date, days=250):
    """过滤新股（250天版本）"""
    try:
        d_date = transform_date(date, 'd')
        filtered_list = []
        for stock in initial_list:
            try:
                stock_info = get_stock_info(stock)
                if stock_info and 'start_date' in stock_info:
                    start_date = stock_info['start_date']
                    if isinstance(start_date, str):
                        start_date = dt.datetime.strptime(start_date, '%Y-%m-%d').date()
                    elif isinstance(start_date, dt.datetime):
                        start_date = start_date.date()

                    if d_date - start_date > dt.timedelta(days=days):
                        filtered_list.append(stock)
            except Exception as e:
                continue
        return filtered_list
    except Exception as e:
        log.info('过滤新股（250天）时出错: {}'.format(str(e)))
        return initial_list


# 每日初始股票池（250天版本）
def prepare_stock_list2(date):
    """准备股票池（250天版本）"""
    try:
        # 获取A股列表
        initial_list = get_Ashares(date)
        if not initial_list:
            return []

        initial_list = filter_kcbj_stock(initial_list)
        initial_list = filter_new_stock2(initial_list, date)
        initial_list = filter_st_stock(initial_list, date)
        initial_list = filter_paused_stock(initial_list, date)
        return initial_list
    except Exception as e:
        log.info('准备股票池（250天版本）时出错: {}'.format(str(e)))
        return []


# 计算股票处于一段时间内相对位置
def get_relative_position_df(stock_list, date, watch_days):
    """计算相对位置"""
    try:
        if len(stock_list) == 0:
            return pd.DataFrame(columns=['rp'])

        result_data = {}
        for stock in stock_list:
            try:
                price_data = get_history(watch_days, '1d', ['high', 'low', 'close'], stock)
                if price_data.empty or len(price_data) < watch_days:
                    continue

                close = price_data['close'].iloc[-1]
                high = price_data['high'].max()
                low = price_data['low'].min()

                if high != low:  # 避免除零错误
                    rp = (close - low) / (high - low)
                    result_data[stock] = rp
            except Exception as e:
                continue

        if result_data:
            result = pd.DataFrame(index=list(result_data.keys()), data={'rp': list(result_data.values())})
            return result
        else:
            return pd.DataFrame(columns=['rp'])
    except Exception as e:
        log.info('计算相对位置时出错: {}'.format(str(e)))
        return pd.DataFrame(columns=['rp'])


def handle_data(context, data):
    """主处理函数"""
    pass
