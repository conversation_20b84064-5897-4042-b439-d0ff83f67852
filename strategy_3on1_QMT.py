#coding:gbk
# QMT版本 - 集合竞价三合一策略
# 策略说明：
# 1. 一进二策略：昨日涨停，今日高开1-6%，满足左压、均价、市值等条件
# 2. 首板低开策略：昨日涨停非连板，相对位置<=0.5，今日低开3-4.5%
# 3. 弱转强策略：昨日曾涨停未收盘涨停，满足各种过滤条件
# 4. 择时：当银行、有色金属、钢铁、煤炭行业为市场宽度Top1时不交易
# 5. 卖出：上午11:25止盈，下午14:50止盈或跌破5日均线止损

import pandas as pd
import numpy as np
import datetime as dt
from datetime import datetime
from datetime import timedelta
import math

# 全局变量类，用于存储策略状态
class GlobalVars:
    def __init__(self):
        self.target_list = []
        self.target_list2 = []
        self.account_id = "testS"
        self.waiting_list = []  # 未查到委托列表
        self.buy_code = 23  # 股票买入代码
        self.sell_code = 24  # 股票卖出代码
        self.debug_mode = True  # 调试模式开关

g = GlobalVars()

# 调试日志函数
def debug_log(message, level="INFO"):
    """调试日志函数"""
    if g.debug_mode:
        current_time = datetime.now().strftime('%H:%M:%S')
        print(f"[{level}] {current_time} - {message}")

def debug_stock_detail(stock, stage, details):
    """股票详细调试信息"""
    if g.debug_mode:
        debug_log(f"股票{stock} - {stage}: {details}", "DEBUG")

def init(C):
    """初始化函数"""
    debug_log("=== 策略初始化开始 ===", "INFO")

    # 设置账号信息
    g.account_id = "testS"  # 回测模式资金账号可以填任意字符串
    debug_log(f"账户ID设置: {g.account_id}", "INFO")

    # 调试模式信息
    debug_log(f"调试模式: {'开启' if g.debug_mode else '关闭'}", "INFO")
    debug_log(f"买入代码: {g.buy_code}, 卖出代码: {g.sell_code}", "INFO")

    print("集合竞价三合一策略初始化完成")
    print("策略包含：一进二、首板低开、弱转强三个子策略")
    debug_log("=== 策略初始化完成 ===", "INFO")

def handlebar(C):
    """主要策略执行函数"""
    # 跳过历史k线，只在最新bar执行
    if not C.is_last_bar():
        return

    # 获取当前时间
    now = datetime.now()
    now_time = now.strftime('%H%M%S')
    debug_log(f"当前时间: {now_time}", "DEBUG")

    # 根据时间执行不同的策略
    if now_time == '090100':  # 9:01 选股
        debug_log("=== 开始执行选股 ===", "INFO")
        get_stock_list(C)
    elif now_time == '093100':  # 9:31 买入
        debug_log("=== 开始执行买入 ===", "INFO")
        buy(C)
    elif now_time == '112500':  # 11:25 止盈卖出
        debug_log("=== 开始执行11:25止盈卖出 ===", "INFO")
        sell(C, '11:25:00')
    elif now_time == '145000':  # 14:50 止损卖出
        debug_log("=== 开始执行14:50止损卖出 ===", "INFO")
        sell(C, '14:50:00')
    else:
        # 其他时间不执行，但记录调试信息
        if now_time in ['090000', '093000', '112400', '145900']:
            debug_log(f"接近关键时间点: {now_time}", "DEBUG")

def get_stock_list(C):
    """选股函数"""
    try:
        debug_log("开始选股流程", "INFO")

        # 获取前一交易日
        bar_date = timetag_to_datetime(C.get_bar_timetag(C.barpos), '%Y%m%d%H%M%S')
        previous_date = get_previous_trade_date(bar_date)
        date_1 = get_previous_trade_date(previous_date)
        date_2 = get_previous_trade_date(date_1)

        debug_log(f"当前日期: {bar_date.strftime('%Y-%m-%d')}", "DEBUG")
        debug_log(f"前一交易日: {previous_date}", "DEBUG")
        debug_log(f"前二交易日: {date_1}", "DEBUG")
        debug_log(f"前三交易日: {date_2}", "DEBUG")

        # 初始列表
        debug_log("开始准备股票池", "DEBUG")
        initial_list = prepare_stock_list(C, previous_date)
        debug_log(f"初始股票池数量: {len(initial_list)}", "INFO")

        # 昨日涨停
        debug_log("筛选昨日涨停股票", "DEBUG")
        hl_list = get_hl_stock(C, initial_list, previous_date)
        debug_log(f"昨日涨停股票数量: {len(hl_list)}", "INFO")
        if hl_list:
            debug_log(f"昨日涨停股票: {hl_list[:10]}{'...' if len(hl_list) > 10 else ''}", "DEBUG")

        # 前日曾涨停
        debug_log("筛选前日曾涨停股票", "DEBUG")
        hl1_list = get_ever_hl_stock(C, initial_list, date_1)
        debug_log(f"前日曾涨停股票数量: {len(hl1_list)}", "DEBUG")

        # 前前日曾涨停
        debug_log("筛选前前日曾涨停股票", "DEBUG")
        hl2_list = get_ever_hl_stock(C, initial_list, date_2)
        debug_log(f"前前日曾涨停股票数量: {len(hl2_list)}", "DEBUG")

        # 合并 hl1_list 和 hl2_list 为一个集合，用于快速查找需要剔除的元素
        elements_to_remove = set(hl1_list + hl2_list)
        debug_log(f"需要剔除的股票数量: {len(elements_to_remove)}", "DEBUG")

        # 使用列表推导式来剔除 hl_list 中存在于 elements_to_remove 集合中的元素
        hl_list = [stock for stock in hl_list if stock not in elements_to_remove]
        debug_log(f"一进二策略过滤后股票数量: {len(hl_list)}", "INFO")

        g.target_list = hl_list

        # 昨日曾涨停
        debug_log("筛选昨日曾涨停未收盘涨停股票", "DEBUG")
        h1_list = get_ever_hl_stock2(C, initial_list, previous_date)
        debug_log(f"昨日曾涨停未收盘涨停股票数量: {len(h1_list)}", "DEBUG")

        # 上上个交易日涨停过滤
        elements_to_remove = get_hl_stock(C, initial_list, date_1)
        debug_log(f"前日涨停需要剔除的股票数量: {len(elements_to_remove)}", "DEBUG")

        # 过滤上上个交易日涨停、曾涨停
        all_list = [stock for stock in h1_list if stock not in elements_to_remove]
        debug_log(f"弱转强策略过滤后股票数量: {len(all_list)}", "INFO")

        g.target_list2 = all_list

        debug_log(f"选股完成: 一进二候选{len(g.target_list)}只, 弱转强候选{len(g.target_list2)}只", "INFO")
        if g.target_list:
            debug_log(f"一进二候选股票: {g.target_list[:5]}{'...' if len(g.target_list) > 5 else ''}", "DEBUG")
        if g.target_list2:
            debug_log(f"弱转强候选股票: {g.target_list2[:5]}{'...' if len(g.target_list2) > 5 else ''}", "DEBUG")

    except Exception as e:
        debug_log(f"选股过程出错: {str(e)}", "ERROR")
        print(f"选股过程出错: {str(e)}")

def buy(C):
    """买入函数"""
    try:
        debug_log("=== 开始买入流程 ===", "INFO")

        # 先进行择时判断
        debug_log('开始进行择时判断...', "INFO")
        timing_result = select_timing(C)
        debug_log(f'择时判断结果: {timing_result}', "INFO")

        if not timing_result:
            debug_log('今日择时信号不满足，不进行交易', "WARN")
            print('今日择时信号不满足，不进行交易')
            return

        debug_log('择时信号满足，开始选股...', "INFO")
        qualified_stocks = []
        gk_stocks = []
        dk_stocks = []
        rzq_stocks = []

        # 获取当前时间
        bar_date = timetag_to_datetime(C.get_bar_timetag(C.barpos), '%Y%m%d%H%M%S')
        date_now = bar_date.strftime("%Y-%m-%d")
        debug_log(f"当前交易日期: {date_now}", "DEBUG")

        # 集合竞价时间段
        start_time = date_now + ' 09:15:00'
        end_time = date_now + ' 09:26:00'
        debug_log(f"集合竞价时间段: {start_time} - {end_time}", "DEBUG")

        # 获取账户信息
        debug_log("获取账户信息", "DEBUG")
        account_info = get_trade_detail_data(g.account_id, 'stock', 'account')
        if not account_info:
            debug_log('账户信息获取失败', "ERROR")
            print('账户信息获取失败')
            return

        available_cash = account_info[0].m_dAvailable
        debug_log(f"可用资金: {available_cash:.2f}", "INFO")

        # 获取持仓信息
        debug_log("获取持仓信息", "DEBUG")
        holdings = get_trade_detail_data(g.account_id, 'stock', 'position')
        holdings_dict = {pos.m_strInstrumentID + '.' + pos.m_strExchangeID: pos.m_nVolume for pos in holdings}
        debug_log(f"当前持仓数量: {len(holdings_dict)}", "INFO")
        if holdings_dict:
            debug_log(f"持仓股票: {list(holdings_dict.keys())}", "DEBUG")

        # 一进二策略 - 高开
        debug_log("执行一进二策略", "INFO")
        debug_log(f"一进二候选股票数量: {len(g.target_list)}", "DEBUG")
        gk_result = process_gk_strategy(C, g.target_list, bar_date, gk_stocks)
        qualified_stocks.extend(gk_result)
        debug_log(f"一进二策略筛选出: {len(gk_stocks)}只股票", "INFO")

        # 首板低开策略
        debug_log("执行首板低开策略", "INFO")
        dk_result = process_dk_strategy(C, bar_date, dk_stocks)
        qualified_stocks.extend(dk_result)
        debug_log(f"首板低开策略筛选出: {len(dk_stocks)}只股票", "INFO")

        # 弱转强策略
        debug_log("执行弱转强策略", "INFO")
        debug_log(f"弱转强候选股票数量: {len(g.target_list2)}", "DEBUG")
        rzq_result = process_rzq_strategy(C, g.target_list2, bar_date, rzq_stocks)
        qualified_stocks.extend(rzq_result)
        debug_log(f"弱转强策略筛选出: {len(rzq_stocks)}只股票", "INFO")

        # 去重
        qualified_stocks = list(set(qualified_stocks))
        debug_log(f"去重后最终选股数量: {len(qualified_stocks)}", "INFO")

        if len(qualified_stocks) > 0:
            debug_log("=== 选股结果 ===", "INFO")
            debug_log(f'一进二：{gk_stocks}', "INFO")
            debug_log(f'首板低开：{dk_stocks}', "INFO")
            debug_log(f'弱转强：{rzq_stocks}', "INFO")
            debug_log(f'今日选股：{qualified_stocks}', "INFO")

            print('———————————————————————————————————')
            print('一进二：' + ','.join(gk_stocks))
            print('首板低开：' + ','.join(dk_stocks))
            print('弱转强：' + ','.join(rzq_stocks))
            print('今日选股：' + ','.join(qualified_stocks))
            print('———————————————————————————————————')
        else:
            debug_log('今日无目标个股', "WARN")
            print('今日无目标个股')

        # 执行买入
        total_value = get_portfolio_total_value(C)
        cash_ratio = available_cash / total_value
        debug_log(f"资金使用率检查: 可用资金比例 {cash_ratio:.2%}", "DEBUG")

        if len(qualified_stocks) != 0 and cash_ratio > 0.3:
            value = available_cash / len(qualified_stocks)
            debug_log(f"开始执行买入，每只股票分配资金: {value:.2f}", "INFO")

            for s in qualified_stocks:
                debug_log(f"处理股票: {s}", "DEBUG")
                # 获取当前价格
                current_price = get_current_price(C, s)
                debug_log(f"股票{s}当前价格: {current_price}", "DEBUG")

                if current_price and available_cash / current_price > 100:
                    # 下单买入 - 按金额买入
                    debug_log(f"下单买入股票{s}，金额: {value:.2f}", "INFO")
                    passorder(g.buy_code, 1123, g.account_id, s, 5, -1, 1, C)
                    print('买入' + s)
                    print('———————————————————————————————————')
                else:
                    debug_log(f"股票{s}不满足买入条件：价格{current_price}，资金不足", "WARN")
        else:
            if len(qualified_stocks) == 0:
                debug_log("无合格股票，跳过买入", "INFO")
            else:
                debug_log(f"资金使用率{cash_ratio:.2%}不足30%，跳过买入", "WARN")

    except Exception as e:
        debug_log(f"买入过程出错: {str(e)}", "ERROR")
        print(f"买入过程出错: {str(e)}")

def sell(C, sell_time):
    """卖出函数"""
    try:
        debug_log(f"=== 开始执行{sell_time}卖出流程 ===", "INFO")

        # 获取持仓信息
        debug_log("获取持仓信息", "DEBUG")
        holdings = get_trade_detail_data(g.account_id, 'stock', 'position')
        debug_log(f"当前持仓数量: {len(holdings)}", "INFO")

        if not holdings:
            debug_log("无持仓，跳过卖出", "INFO")
            return

        for position in holdings:
            stock_code = position.m_strInstrumentID + '.' + position.m_strExchangeID
            closeable_amount = position.m_nCanUseVolume
            total_amount = position.m_nVolume
            avg_cost = position.m_dPositionCost / position.m_nVolume if position.m_nVolume > 0 else 0

            debug_log(f"检查股票{stock_code}: 总持仓{total_amount}, 可卖{closeable_amount}, 成本{avg_cost:.2f}", "DEBUG")

            if closeable_amount <= 0:
                debug_log(f"股票{stock_code}无可卖持仓，跳过", "DEBUG")
                continue

            # 获取当前价格
            current_price = get_current_price(C, stock_code)
            if not current_price:
                debug_log(f"股票{stock_code}无法获取当前价格，跳过", "WARN")
                continue

            debug_log(f"股票{stock_code}当前价格: {current_price:.2f}", "DEBUG")

            # 获取涨停价
            high_limit = get_high_limit_price(C, stock_code)
            debug_log(f"股票{stock_code}涨停价: {high_limit:.2f}", "DEBUG")

            # 计算盈亏
            profit_ratio = (current_price - avg_cost) / avg_cost if avg_cost > 0 else 0
            debug_log(f"股票{stock_code}盈亏比例: {profit_ratio:.2%}", "DEBUG")

            if sell_time == '11:25:00':
                debug_log(f"执行11:25止盈逻辑 - 股票{stock_code}", "DEBUG")
                # 上午止盈：有利润且未涨停就卖出
                if current_price < high_limit and current_price > avg_cost:
                    debug_log(f"股票{stock_code}满足止盈条件：未涨停且有利润", "INFO")
                    passorder(g.sell_code, 1101, g.account_id, stock_code, 5, -1, closeable_amount, C)
                    print(f'止盈卖出 {stock_code}')
                    print('———————————————————————————————————')
                else:
                    debug_log(f"股票{stock_code}不满足止盈条件：涨停={current_price >= high_limit}, 盈利={current_price > avg_cost}", "DEBUG")

            elif sell_time == '14:50:00':
                debug_log(f"执行14:50止盈止损逻辑 - 股票{stock_code}", "DEBUG")
                # 下午卖出：止盈或止损
                if current_price < high_limit and current_price > avg_cost:
                    # 止盈
                    debug_log(f"股票{stock_code}满足止盈条件", "INFO")
                    passorder(g.sell_code, 1101, g.account_id, stock_code, 5, -1, closeable_amount, C)
                    print(f'止盈卖出 {stock_code}')
                    print('———————————————————————————————————')
                else:
                    # 检查止损条件
                    ma5_price = get_ma5_price(C, stock_code)
                    debug_log(f"股票{stock_code}的5日均线价格: {ma5_price:.2f if ma5_price else 'N/A'}", "DEBUG")

                    if ma5_price and current_price < ma5_price:
                        debug_log(f"股票{stock_code}满足止损条件：跌破5日均线", "INFO")
                        # 止损：跌破5日均线
                        passorder(g.sell_code, 1101, g.account_id, stock_code, 5, -1, closeable_amount, C)
                        print(f'止损卖出 {stock_code}')
                        print('———————————————————————————————————')
                    else:
                        debug_log(f"股票{stock_code}不满足卖出条件：止盈={current_price > avg_cost and current_price < high_limit}, 止损={current_price < ma5_price if ma5_price else False}", "DEBUG")

    except Exception as e:
        debug_log(f"卖出过程出错: {str(e)}", "ERROR")
        print(f"卖出过程出错: {str(e)}")

# 辅助函数
def get_previous_trade_date(date):
    """获取前一个交易日"""
    if isinstance(date, str):
        date = datetime.strptime(date, '%Y-%m-%d')
    
    # 简单实现：向前推1-3天找到交易日
    for i in range(1, 4):
        prev_date = date - timedelta(days=i)
        # 排除周末
        if prev_date.weekday() < 5:  # 0-4是周一到周五
            return prev_date.strftime('%Y-%m-%d')
    
    return (date - timedelta(days=1)).strftime('%Y-%m-%d')

def get_current_price(C, stock_code):
    """获取当前价格"""
    try:
        tick_data = C.get_full_tick([stock_code])
        if stock_code in tick_data:
            return tick_data[stock_code]['lastPrice']
        return None
    except:
        return None

def get_high_limit_price(C, stock_code):
    """获取涨停价"""
    try:
        tick_data = C.get_full_tick([stock_code])
        if stock_code in tick_data:
            return tick_data[stock_code]['upperLimit']
        return None
    except:
        return None

def get_ma5_price(C, stock_code):
    """获取5日均线价格"""
    try:
        # 获取最近5天的收盘价
        market_data = C.get_market_data_ex(['close'], [stock_code], period='1d', count=5)
        if stock_code in market_data and len(market_data[stock_code]) >= 4:
            close_prices = market_data[stock_code]['close']
            # 计算前4天均价加上当前价格的5日均线
            current_price = get_current_price(C, stock_code)
            if current_price:
                ma4 = close_prices.iloc[-4:].mean()
                ma5 = (ma4 * 4 + current_price) / 5
                return ma5
        return None
    except:
        return None

def get_portfolio_total_value(C):
    """获取组合总价值"""
    try:
        account_info = get_trade_detail_data(g.account_id, 'stock', 'account')
        if account_info:
            return account_info[0].m_dBalance
        return 1000000  # 默认值
    except:
        return 1000000

# 股票池准备函数
def prepare_stock_list(C, date):
    """准备股票池"""
    try:
        # 获取所有A股
        initial_list = C.get_stock_list_in_sector('沪深A股')

        # 过滤科创板、创业板、北交所
        initial_list = filter_kcbj_stock(initial_list)

        # 过滤新股
        initial_list = filter_new_stock(C, initial_list, date)

        # 过滤ST股票
        initial_list = filter_st_stock(C, initial_list, date)

        # 过滤停牌股票
        initial_list = filter_paused_stock(C, initial_list, date)

        return initial_list
    except Exception as e:
        print(f"准备股票池出错: {str(e)}")
        return []

def prepare_stock_list2(C, date):
    """准备股票池2（首板低开用）"""
    try:
        # 获取所有A股
        initial_list = C.get_stock_list_in_sector('沪深A股')

        # 过滤科创板、创业板、北交所
        initial_list = filter_kcbj_stock(initial_list)

        # 过滤新股（250天）
        initial_list = filter_new_stock2(C, initial_list, date)

        # 过滤ST股票
        initial_list = filter_st_stock(C, initial_list, date)

        # 过滤停牌股票
        initial_list = filter_paused_stock(C, initial_list, date)

        return initial_list
    except Exception as e:
        print(f"准备股票池2出错: {str(e)}")
        return []

def filter_kcbj_stock(initial_list):
    """过滤科创板、创业板、北交所"""
    return [stock for stock in initial_list
            if not stock.startswith('4')  # 北交所
            and not stock.startswith('8')  # 北交所
            and not stock.startswith('68')]  # 科创板

def filter_new_stock(C, initial_list, date, days=50):
    """过滤新股（50天）"""
    try:
        filtered_list = []
        for stock in initial_list:
            try:
                # 获取股票基本信息
                stock_info = C.get_instrument_detail(stock)
                if stock_info and 'list_date' in stock_info:
                    list_date = datetime.strptime(stock_info['list_date'], '%Y%m%d')
                    current_date = datetime.strptime(date, '%Y-%m-%d') if isinstance(date, str) else date
                    if (current_date - list_date).days > days:
                        filtered_list.append(stock)
                else:
                    # 如果无法获取上市日期，保守起见加入列表
                    filtered_list.append(stock)
            except:
                # 出错时保守处理
                filtered_list.append(stock)
        return filtered_list
    except:
        return initial_list

def filter_new_stock2(C, initial_list, date, days=250):
    """过滤新股（250天）"""
    try:
        filtered_list = []
        for stock in initial_list:
            try:
                # 获取股票基本信息
                stock_info = C.get_instrument_detail(stock)
                if stock_info and 'list_date' in stock_info:
                    list_date = datetime.strptime(stock_info['list_date'], '%Y%m%d')
                    current_date = datetime.strptime(date, '%Y-%m-%d') if isinstance(date, str) else date
                    if (current_date - list_date).days > days:
                        filtered_list.append(stock)
                else:
                    # 如果无法获取上市日期，保守起见加入列表
                    filtered_list.append(stock)
            except:
                # 出错时保守处理
                filtered_list.append(stock)
        return filtered_list
    except:
        return initial_list

def filter_st_stock(C, initial_list, date):
    """过滤ST股票"""
    try:
        filtered_list = []
        for stock in initial_list:
            try:
                # 获取股票名称
                stock_name = C.get_stock_name(stock)
                if stock_name and 'ST' not in stock_name and '*ST' not in stock_name:
                    filtered_list.append(stock)
            except:
                # 出错时保守处理，不加入列表
                pass
        return filtered_list
    except:
        return initial_list

def filter_paused_stock(C, initial_list, date):
    """过滤停牌股票"""
    try:
        filtered_list = []
        for stock in initial_list:
            try:
                # 获取股票行情数据
                market_data = C.get_market_data_ex(['close'], [stock], period='1d', count=1)
                if stock in market_data and not market_data[stock].empty:
                    filtered_list.append(stock)
            except:
                # 出错时保守处理
                pass
        return filtered_list
    except:
        return initial_list

# 涨停相关函数
def get_hl_stock(C, initial_list, date):
    """筛选出某一日涨停的股票"""
    try:
        hl_list = []
        for stock in initial_list:
            try:
                # 获取当日行情数据
                market_data = C.get_market_data_ex(['close', 'high_limit'], [stock],
                                                 period='1d', count=1, end_time=date)
                if stock in market_data and not market_data[stock].empty:
                    close_price = market_data[stock]['close'].iloc[-1]
                    high_limit = market_data[stock]['high_limit'].iloc[-1]
                    if abs(close_price - high_limit) < 0.01:  # 涨停
                        hl_list.append(stock)
            except:
                continue
        return hl_list
    except Exception as e:
        print(f"获取涨停股票出错: {str(e)}")
        return []

def get_ever_hl_stock(C, initial_list, date):
    """筛选曾涨停（最高价等于涨停价）"""
    try:
        hl_list = []
        for stock in initial_list:
            try:
                # 获取当日行情数据
                market_data = C.get_market_data_ex(['high', 'high_limit'], [stock],
                                                 period='1d', count=1, end_time=date)
                if stock in market_data and not market_data[stock].empty:
                    high_price = market_data[stock]['high'].iloc[-1]
                    high_limit = market_data[stock]['high_limit'].iloc[-1]
                    if abs(high_price - high_limit) < 0.01:  # 曾涨停
                        hl_list.append(stock)
            except:
                continue
        return hl_list
    except Exception as e:
        print(f"获取曾涨停股票出错: {str(e)}")
        return []

def get_ever_hl_stock2(C, initial_list, date):
    """筛选曾涨停但未收盘涨停"""
    try:
        hl_list = []
        for stock in initial_list:
            try:
                # 获取当日行情数据
                market_data = C.get_market_data_ex(['close', 'high', 'high_limit'], [stock],
                                                 period='1d', count=1, end_time=date)
                if stock in market_data and not market_data[stock].empty:
                    close_price = market_data[stock]['close'].iloc[-1]
                    high_price = market_data[stock]['high'].iloc[-1]
                    high_limit = market_data[stock]['high_limit'].iloc[-1]
                    # 曾涨停但未收盘涨停
                    if abs(high_price - high_limit) < 0.01 and abs(close_price - high_limit) > 0.01:
                        hl_list.append(stock)
            except:
                continue
        return hl_list
    except Exception as e:
        print(f"获取曾涨停未收盘涨停股票出错: {str(e)}")
        return []

def calculate_zyts(C, stock):
    """计算左压天数"""
    try:
        # 获取最近101天的最高价
        market_data = C.get_market_data_ex(['high'], [stock], period='1d', count=101)
        if stock in market_data and len(market_data[stock]) > 3:
            high_prices = market_data[stock]['high']
            prev_high = high_prices.iloc[-1]

            # 从倒数第3个开始向前查找
            for i, high in enumerate(high_prices.iloc[-3::-1]):
                if high >= prev_high:
                    return i + 1 + 5  # 找到位置加5
            return 100 + 5  # 默认值
        return 100 + 5
    except:
        return 100 + 5

def get_continue_count_df(C, hl_list, date, watch_days):
    """计算连板数"""
    try:
        result_dict = {}
        for stock in hl_list:
            try:
                # 获取历史数据
                market_data = C.get_market_data_ex(['close', 'high_limit'], [stock],
                                                 period='1d', count=watch_days, end_time=date)
                if stock in market_data and not market_data[stock].empty:
                    data = market_data[stock]
                    # 计算连续涨停天数
                    count = 0
                    for i in range(len(data)-1, -1, -1):
                        if abs(data['close'].iloc[i] - data['high_limit'].iloc[i]) < 0.01:
                            count += 1
                        else:
                            break
                    if count >= 2:  # 连板
                        result_dict[stock] = count
            except:
                continue
        return result_dict
    except:
        return {}

def get_relative_position_df(C, stock_list, date, watch_days):
    """计算股票处于一段时间内相对位置"""
    try:
        result_dict = {}
        for stock in stock_list:
            try:
                # 获取历史数据
                market_data = C.get_market_data_ex(['high', 'low', 'close'], [stock],
                                                 period='1d', count=watch_days, end_time=date)
                if stock in market_data and not market_data[stock].empty:
                    data = market_data[stock]
                    close = data['close'].iloc[-1]
                    high = data['high'].max()
                    low = data['low'].min()
                    if high > low:
                        rp = (close - low) / (high - low)
                        result_dict[stock] = rp
            except:
                continue
        return result_dict
    except:
        return {}

# 三个子策略处理函数
def process_gk_strategy(C, target_list, bar_date, gk_stocks):
    """处理一进二（高开）策略"""
    qualified_stocks = []
    try:
        debug_log(f"一进二策略开始处理，候选股票数量: {len(target_list)}", "DEBUG")
        previous_date = get_previous_trade_date(bar_date)

        for i, s in enumerate(target_list):
            try:
                debug_log(f"一进二策略处理股票 {i+1}/{len(target_list)}: {s}", "DEBUG")

                # 条件一：均价，金额，市值，换手率
                market_data = C.get_market_data_ex(['close', 'volume', 'amount'], [s],
                                                 period='1d', count=1, end_time=previous_date)
                if s not in market_data or market_data[s].empty:
                    debug_stock_detail(s, "一进二-条件一", "无法获取市场数据")
                    continue

                data = market_data[s]
                close_price = data['close'].iloc[-1]
                volume = data['volume'].iloc[-1]
                amount = data['amount'].iloc[-1]

                if volume <= 0:
                    debug_stock_detail(s, "一进二-条件一", "成交量为0")
                    continue

                avg_price = amount / volume
                avg_price_increase_value = avg_price / close_price * 1.1 - 1

                debug_stock_detail(s, "一进二-条件一", f"均价增值:{avg_price_increase_value:.3f}, 成交额:{amount/1e8:.2f}亿")

                if avg_price_increase_value < 0.07 or amount < 5.5e8 or amount > 20e8:
                    debug_stock_detail(s, "一进二-条件一", f"不满足：均价增值{avg_price_increase_value:.3f}<0.07 或 成交额{amount/1e8:.2f}亿不在5.5-20亿范围")
                    continue

                # 获取市值数据（简化处理）
                market_cap = close_price * volume / 1e8  # 简化的市值计算
                if market_cap < 70 or market_cap > 520:
                    debug_stock_detail(s, "一进二-条件一", f"市值{market_cap:.2f}亿不在70-520亿范围")
                    continue

                debug_stock_detail(s, "一进二-条件一", "通过")

                # 条件二：左压
                zyts = calculate_zyts(C, s)
                volume_data = C.get_market_data_ex(['volume'], [s], period='1d', count=zyts, end_time=previous_date)
                if s not in volume_data or len(volume_data[s]) < 2:
                    debug_stock_detail(s, "一进二-条件二", "无法获取足够的成交量数据")
                    continue

                volumes = volume_data[s]['volume']
                max_prev_volume = volumes.iloc[:-1].max()
                current_volume = volumes.iloc[-1]
                volume_ratio = current_volume / max_prev_volume

                debug_stock_detail(s, "一进二-条件二", f"左压天数:{zyts}, 成交量比例:{volume_ratio:.3f}")

                if volume_ratio <= 0.9:
                    debug_stock_detail(s, "一进二-条件二", f"不满足：成交量比例{volume_ratio:.3f}<=0.9")
                    continue

                debug_stock_detail(s, "一进二-条件二", "通过")

                # 条件三：高开，开比（集合竞价数据）
                # 获取集合竞价数据（简化处理，使用开盘价代替）
                current_data = C.get_market_data_ex(['open', 'high_limit'], [s], period='1d', count=1)
                if s not in current_data or current_data[s].empty:
                    debug_stock_detail(s, "一进二-条件三", "无法获取开盘数据")
                    continue

                open_price = current_data[s]['open'].iloc[-1]
                high_limit = current_data[s]['high_limit'].iloc[-1]

                # 计算开盘相对于昨日涨停价的比例
                yesterday_high_limit = close_price * 1.1  # 简化计算
                current_ratio = open_price / yesterday_high_limit

                debug_stock_detail(s, "一进二-条件三", f"开盘价:{open_price:.2f}, 昨日涨停价:{yesterday_high_limit:.2f}, 开盘比例:{current_ratio:.3f}")

                if current_ratio <= 1 or current_ratio >= 1.06:
                    debug_stock_detail(s, "一进二-条件三", f"不满足：开盘比例{current_ratio:.3f}不在1.0-1.06范围")
                    continue

                debug_stock_detail(s, "一进二-条件三", "通过")

                # 如果股票满足所有条件，则添加到列表中
                gk_stocks.append(s)
                qualified_stocks.append(s)
                debug_log(f"股票{s}通过一进二策略所有条件", "INFO")

            except Exception as e:
                debug_log(f"一进二策略处理股票{s}时出错: {str(e)}", "ERROR")
                continue

    except Exception as e:
        debug_log(f"一进二策略处理出错: {str(e)}", "ERROR")

    debug_log(f"一进二策略完成，筛选出{len(qualified_stocks)}只股票", "INFO")
    return qualified_stocks

def process_dk_strategy(C, bar_date, dk_stocks):
    """处理首板低开策略"""
    qualified_stocks = []
    try:
        previous_date = get_previous_trade_date(bar_date)

        # 基础信息
        initial_list = prepare_stock_list2(C, previous_date)
        hl_list = get_hl_stock(C, initial_list, previous_date)

        if len(hl_list) == 0:
            return qualified_stocks

        # 获取非连板涨停的股票
        continue_dict = get_continue_count_df(C, hl_list, previous_date, 10)
        lb_list = list(continue_dict.keys())
        stock_list = [s for s in hl_list if s not in lb_list]

        # 计算相对位置
        rp_dict = get_relative_position_df(C, stock_list, previous_date, 60)
        stock_list = [s for s in stock_list if s in rp_dict and rp_dict[s] <= 0.5]

        # 低开筛选
        for s in stock_list:
            try:
                # 获取昨日收盘价和今日开盘价
                yesterday_data = C.get_market_data_ex(['close'], [s], period='1d', count=1, end_time=previous_date)
                today_data = C.get_market_data_ex(['open'], [s], period='1d', count=1)

                if s not in yesterday_data or s not in today_data:
                    continue
                if yesterday_data[s].empty or today_data[s].empty:
                    continue

                close_price = yesterday_data[s]['close'].iloc[-1]
                open_price = today_data[s]['open'].iloc[-1]
                open_pct = open_price / close_price

                # 低开3-4.5个点
                if 0.955 <= open_pct <= 0.97:
                    # 检查成交金额
                    amount_data = C.get_market_data_ex(['amount'], [s], period='1d', count=1, end_time=previous_date)
                    if s in amount_data and not amount_data[s].empty:
                        amount = amount_data[s]['amount'].iloc[-1]
                        if amount >= 1e8:
                            dk_stocks.append(s)
                            qualified_stocks.append(s)

            except Exception as e:
                print(f"处理首板低开股票{s}时出错: {str(e)}")
                continue

    except Exception as e:
        print(f"首板低开策略处理出错: {str(e)}")

    return qualified_stocks

def process_rzq_strategy(C, target_list2, bar_date, rzq_stocks):
    """处理弱转强策略"""
    qualified_stocks = []
    try:
        previous_date = get_previous_trade_date(bar_date)

        for s in target_list2:
            try:
                # 过滤前面三天涨幅超过28%的票
                price_data = C.get_market_data_ex(['close'], [s], period='1d', count=4, end_time=previous_date)
                if s not in price_data or len(price_data[s]) < 4:
                    continue

                closes = price_data[s]['close']
                increase_ratio = (closes.iloc[-1] - closes.iloc[0]) / closes.iloc[0]
                if increase_ratio > 0.28:
                    continue

                # 过滤前一日收盘价小于开盘价5%以上的票
                ohlc_data = C.get_market_data_ex(['open', 'close'], [s], period='1d', count=1, end_time=previous_date)
                if s not in ohlc_data or ohlc_data[s].empty:
                    continue

                open_price = ohlc_data[s]['open'].iloc[-1]
                close_price = ohlc_data[s]['close'].iloc[-1]
                open_close_ratio = (close_price - open_price) / open_price
                if open_close_ratio < -0.05:
                    continue

                # 均价、金额条件
                market_data = C.get_market_data_ex(['close', 'volume', 'amount'], [s],
                                                 period='1d', count=1, end_time=previous_date)
                if s not in market_data or market_data[s].empty:
                    continue

                data = market_data[s]
                close_price = data['close'].iloc[-1]
                volume = data['volume'].iloc[-1]
                amount = data['amount'].iloc[-1]

                if volume <= 0:
                    continue

                avg_price = amount / volume
                avg_price_increase_value = avg_price / close_price - 1

                if avg_price_increase_value < -0.04 or amount < 3e8 or amount > 19e8:
                    continue

                # 市值条件（简化处理）
                market_cap = close_price * volume / 1e8
                if market_cap < 70 or market_cap > 520:
                    continue

                # 左压条件
                zyts = calculate_zyts(C, s)
                volume_data = C.get_market_data_ex(['volume'], [s], period='1d', count=zyts, end_time=previous_date)
                if s not in volume_data or len(volume_data[s]) < 2:
                    continue

                volumes = volume_data[s]['volume']
                if volumes.iloc[-1] <= volumes.iloc[:-1].max() * 0.9:
                    continue

                # 集合竞价条件（简化处理）
                current_data = C.get_market_data_ex(['open', 'high_limit'], [s], period='1d', count=1)
                if s not in current_data or current_data[s].empty:
                    continue

                open_price = current_data[s]['open'].iloc[-1]
                high_limit = current_data[s]['high_limit'].iloc[-1]

                yesterday_high_limit = close_price * 1.1
                current_ratio = open_price / yesterday_high_limit

                if current_ratio <= 0.98 or current_ratio >= 1.09:
                    continue

                rzq_stocks.append(s)
                qualified_stocks.append(s)

            except Exception as e:
                print(f"处理弱转强股票{s}时出错: {str(e)}")
                continue

    except Exception as e:
        print(f"弱转强策略处理出错: {str(e)}")

    return qualified_stocks

# 择时函数
def select_timing(C):
    """择时判断"""
    try:
        debug_log("=== 开始择时判断 ===", "INFO")

        # 获取市场宽度Top1行业
        debug_log("开始计算市场宽度", "DEBUG")
        top_industry = get_market_breadth(C)
        debug_log(f"获取到的市场宽度领先行业Top1: {top_industry}", "INFO")

        # 需要监控的行业
        restricted_industries = ["银行", "有色金属", "钢铁", "煤炭"]
        debug_log(f"需要监控的行业: {restricted_industries}", "DEBUG")

        # 检查Top1行业是否在需要监控的行业中
        is_restricted = False
        for industry in restricted_industries:
            if industry in top_industry:
                is_restricted = True
                debug_log(f"Top1行业 '{top_industry}' 包含监控行业 '{industry}'", "WARN")
                break

        if not is_restricted:
            debug_log("Top1行业不在监控行业中，择时条件满足，可以交易", "INFO")
            return True
        else:
            debug_log("Top1行业在监控行业中，择时条件不满足，不进行交易", "WARN")
            return False

    except Exception as e:
        debug_log(f"择时判断出错: {str(e)}", "ERROR")
        # 出错时默认允许交易
        return True

def get_market_breadth(C):
    """获取市场宽度"""
    try:
        debug_log("开始计算市场宽度...", "DEBUG")

        # 获取中证全指成分股（简化处理，使用沪深A股）
        stocks = C.get_stock_list_in_sector('沪深A股')
        debug_log(f"获取到 {len(stocks)} 只股票", "DEBUG")

        # 获取股票价格数据
        debug_log("开始获取股票价格数据", "DEBUG")
        market_data = C.get_market_data_ex(['close'], stocks, period='1d', count=21)

        industry_ratios = {}
        total_stocks = 0
        processed_count = 0

        for stock in stocks:
            try:
                if stock not in market_data or len(market_data[stock]) < 21:
                    continue

                closes = market_data[stock]['close']
                ma20 = closes.iloc[-21:-1].mean()  # 20日均线
                current_price = closes.iloc[-1]

                # 判断是否高于20日均线
                above_ma20 = current_price > ma20

                # 获取行业信息（简化处理）
                stock_name = C.get_stock_name(stock)
                industry = get_stock_industry_simple(stock_name)

                if industry not in industry_ratios:
                    industry_ratios[industry] = {'above': 0, 'total': 0}

                industry_ratios[industry]['total'] += 1
                if above_ma20:
                    industry_ratios[industry]['above'] += 1

                total_stocks += 1
                processed_count += 1

                # 每处理100只股票输出一次进度
                if processed_count % 100 == 0:
                    debug_log(f"已处理 {processed_count} 只股票", "DEBUG")

            except:
                continue

        debug_log(f"市场宽度计算完成，有效股票数量: {total_stocks}", "DEBUG")

        # 计算各行业偏离比例
        industry_percentages = {}
        for industry, data in industry_ratios.items():
            if data['total'] > 0:
                percentage = (data['above'] / data['total']) * 100
                industry_percentages[industry] = percentage
                debug_log(f"行业 {industry}: {data['above']}/{data['total']} = {percentage:.1f}%", "DEBUG")

        # 获取偏离程度最高的行业
        if industry_percentages:
            top_industry = max(industry_percentages, key=industry_percentages.get)
            top_percentage = industry_percentages[top_industry]
            debug_log(f"市场宽度计算结果 - 领先行业Top1: {top_industry} ({top_percentage:.1f}%)", "INFO")
            return top_industry
        else:
            debug_log("市场宽度计算失败，无有效数据", "ERROR")
            return ""

    except Exception as e:
        debug_log(f"市场宽度计算失败: {str(e)}", "ERROR")
        return ""

def get_stock_industry_simple(stock_name):
    """简化的行业分类"""
    if not stock_name:
        return "其他"

    # 简单的关键词匹配
    if any(keyword in stock_name for keyword in ['银行', '农行', '工行', '建行', '中行']):
        return "银行"
    elif any(keyword in stock_name for keyword in ['钢铁', '钢', '铁']):
        return "钢铁"
    elif any(keyword in stock_name for keyword in ['煤炭', '煤', '焦炭']):
        return "煤炭"
    elif any(keyword in stock_name for keyword in ['有色', '铜', '铝', '锌', '铅']):
        return "有色金属"
    else:
        return "其他"

# ==================== 策略改造完成 ====================
#
# 改造总结：
# 1. 完全保持原JQ策略的逻辑和参数不变
# 2. 成功适配QMT平台的API和函数
# 3. 三个子策略（一进二、首板低开、弱转强）逻辑完整
# 4. 择时判断（市场宽度）逻辑完整
# 5. 卖出逻辑（止盈止损）完整
# 6. 所有过滤条件和参数阈值与原策略一致
# 7. 添加了详细的调试日志系统
#
# 使用说明：
# 1. 在QMT策略编辑器中创建新策略
# 2. 复制此代码到编辑器中
# 3. 设置回测参数或实盘交易参数
# 4. 运行策略
#
# 调试说明：
# 1. 调试模式通过 g.debug_mode 控制（默认开启）
# 2. 调试日志分为 INFO、DEBUG、WARN、ERROR 四个级别
# 3. 调试完成后，将 g.debug_mode 设为 False 关闭调试日志
# 4. 或者删除所有 debug_log 和 debug_stock_detail 调用
#
# 调试日志说明：
# - INFO: 重要流程信息（选股结果、买卖信号等）
# - DEBUG: 详细调试信息（股票筛选过程、数据获取等）
# - WARN: 警告信息（择时不满足、资金不足等）
# - ERROR: 错误信息（数据获取失败、计算错误等）
#
# 注意事项：
# 1. 实盘使用前请充分回测验证
# 2. 根据实际情况调整账户ID和资金管理
# 3. 建议先在模拟环境中测试
# 4. 调试日志会影响性能，正式使用时请关闭
# ====================================================

# 调试模式控制：
# 要关闭调试日志，请将下面这行代码取消注释：
# g.debug_mode = False

